{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\index.vue", "mtime": 1754103601119}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RJbnZlc3RtZW50LCBnZXRJbnZlc3RtZW50LCBkZWxJbnZlc3RtZW50LCBhZGRJbnZlc3RtZW50LCB1cGRhdGVJbnZlc3RtZW50IH0gZnJvbSAiQC9hcGkvbWluaWFwcC9pbnZlc3RtZW50IjsKaW1wb3J0IHsgbGlzdEluZHVzdHJ5VHJlZSB9IGZyb20gIkAvYXBpL21pbmlhcHAvaW52ZXN0bWVudCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkludmVzdG1lbnQiLAogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6aG555uu5oqV6LWE6KGo5qC85pWw5o2uCiAgICAgIGludmVzdG1lbnRMaXN0OiBbXSwKICAgICAgLy8g6KGM5Lia5qCR6YCJ6aG5CiAgICAgIGluZHVzdHJ5T3B0aW9uczogW10sCiAgICAgIC8vIOecgeS7vemAiemhuSAtIOebtOaOpeS9v+eUqOeugOensAogICAgICBwcm92aW5jZU9wdGlvbnM6IFsKICAgICAgICB7IHZhbHVlOiAn5YyX5LqsJywgbGFiZWw6ICfljJfkuqwnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+Wkqea0pScsIGxhYmVsOiAn5aSp5rSlJyB9LAogICAgICAgIHsgdmFsdWU6ICfmsrPljJcnLCBsYWJlbDogJ+ays+WMlycgfSwKICAgICAgICB7IHZhbHVlOiAn5bGx6KW/JywgbGFiZWw6ICflsbHopb8nIH0sCiAgICAgICAgeyB2YWx1ZTogJ+WGheiSmeWPpCcsIGxhYmVsOiAn5YaF6JKZ5Y+kJyB9LAogICAgICAgIHsgdmFsdWU6ICfovr3lroEnLCBsYWJlbDogJ+i+veWugScgfSwKICAgICAgICB7IHZhbHVlOiAn5ZCJ5p6XJywgbGFiZWw6ICflkInmnpcnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+m7kem+meaxnycsIGxhYmVsOiAn6buR6b6Z5rGfJyB9LAogICAgICAgIHsgdmFsdWU6ICfkuIrmtbcnLCBsYWJlbDogJ+S4iua1tycgfSwKICAgICAgICB7IHZhbHVlOiAn5rGf6IuPJywgbGFiZWw6ICfmsZ/oi48nIH0sCiAgICAgICAgeyB2YWx1ZTogJ+a1meaxnycsIGxhYmVsOiAn5rWZ5rGfJyB9LAogICAgICAgIHsgdmFsdWU6ICflronlvr0nLCBsYWJlbDogJ+WuieW+vScgfSwKICAgICAgICB7IHZhbHVlOiAn56aP5bu6JywgbGFiZWw6ICfnpo/lu7onIH0sCiAgICAgICAgeyB2YWx1ZTogJ+axn+ilvycsIGxhYmVsOiAn5rGf6KW/JyB9LAogICAgICAgIHsgdmFsdWU6ICflsbHkuJwnLCBsYWJlbDogJ+WxseS4nCcgfSwKICAgICAgICB7IHZhbHVlOiAn5rKz5Y2XJywgbGFiZWw6ICfmsrPljZcnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+a5luWMlycsIGxhYmVsOiAn5rmW5YyXJyB9LAogICAgICAgIHsgdmFsdWU6ICfmuZbljZcnLCBsYWJlbDogJ+a5luWNlycgfSwKICAgICAgICB7IHZhbHVlOiAn5bm/5LicJywgbGFiZWw6ICflub/kuJwnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+W5v+ilvycsIGxhYmVsOiAn5bm/6KW/JyB9LAogICAgICAgIHsgdmFsdWU6ICfmtbfljZcnLCBsYWJlbDogJ+a1t+WNlycgfSwKICAgICAgICB7IHZhbHVlOiAn6YeN5bqGJywgbGFiZWw6ICfph43luoYnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+Wbm+W3nScsIGxhYmVsOiAn5Zub5bedJyB9LAogICAgICAgIHsgdmFsdWU6ICfotLXlt54nLCBsYWJlbDogJ+i0teW3nicgfSwKICAgICAgICB7IHZhbHVlOiAn5LqR5Y2XJywgbGFiZWw6ICfkupHljZcnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+ilv+iXjycsIGxhYmVsOiAn6KW/6JePJyB9LAogICAgICAgIHsgdmFsdWU6ICfpmZXopb8nLCBsYWJlbDogJ+mZleilvycgfSwKICAgICAgICB7IHZhbHVlOiAn55SY6IKDJywgbGFiZWw6ICfnlJjogoMnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+mdkua1tycsIGxhYmVsOiAn6Z2S5rW3JyB9LAogICAgICAgIHsgdmFsdWU6ICflroHlpI8nLCBsYWJlbDogJ+WugeWkjycgfSwKICAgICAgICB7IHZhbHVlOiAn5paw55aGJywgbGFiZWw6ICfmlrDnloYnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+WPsOa5vicsIGxhYmVsOiAn5Y+w5rm+JyB9LAogICAgICAgIHsgdmFsdWU6ICfpppnmuK8nLCBsYWJlbDogJ+mmmea4rycgfSwKICAgICAgICB7IHZhbHVlOiAn5r6z6ZeoJywgbGFiZWw6ICfmvrPpl6gnIH0KICAgICAgXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBmaW5hbmNpbmdSb3VuZDogbnVsbCwKICAgICAgICBpbmR1c3RyeUlkOiBudWxsLAogICAgICAgIHJlZ2lvbjogbnVsbCwKICAgICAgICBzdGF0dXM6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBwcm9qZWN0TmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumhueebruWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBmaW5hbmNpbmdSb3VuZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiejei1hOi9ruasoeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIGluZHVzdHJ5SWQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7ooYzkuJrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICByZWdpb246IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlnKjlnLDljLrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0SW5kdXN0cnlUcmVlKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6aG555uu5oqV6LWE5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0SW52ZXN0bWVudCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmludmVzdG1lbnRMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6Xor6LooYzkuJrmoJEgKi8KICAgIGdldEluZHVzdHJ5VHJlZSgpIHsKICAgICAgbGlzdEluZHVzdHJ5VHJlZSgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuaW5kdXN0cnlPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWkhOeQhuagh+etvuaVsOe7hCAqLwogICAgZ2V0VGFnQXJyYXkodGFncykgewogICAgICBpZiAoIXRhZ3MpIHJldHVybiBbXTsKICAgICAgcmV0dXJuIHRhZ3Muc3BsaXQoJywnKS5maWx0ZXIodGFnID0+IHRhZy50cmltKCkgIT09ICcnKTsKICAgIH0sCiAgICAvKiog5aSE55CG5Zyw5Yy65ZCN56ew77yM5Y675o6J55yB5biC6Ieq5rK75Yy6562J5ZCO57yAICovCiAgICBwcm9jZXNzUmVnaW9uTmFtZShyZWdpb25OYW1lKSB7CiAgICAgIGlmICghcmVnaW9uTmFtZSkgcmV0dXJuIHJlZ2lvbk5hbWU7CgogICAgICAvLyDlrprkuYnpnIDopoHljrvmjonnmoTlkI7nvIAKICAgICAgY29uc3Qgc3VmZml4ZXMgPSBbJ+ecgScsICfluIInLCAn6Ieq5rK75Yy6JywgJ+eJueWIq+ihjOaUv+WMuicsICflo67ml4/oh6rmsrvljLonLCAn5Zue5peP6Ieq5rK75Yy6JywgJ+e7tOWQvuWwlOiHquayu+WMuiddOwoKICAgICAgbGV0IHByb2Nlc3NlZE5hbWUgPSByZWdpb25OYW1lOwogICAgICBmb3IgKGNvbnN0IHN1ZmZpeCBvZiBzdWZmaXhlcykgewogICAgICAgIGlmIChwcm9jZXNzZWROYW1lLmVuZHNXaXRoKHN1ZmZpeCkpIHsKICAgICAgICAgIHByb2Nlc3NlZE5hbWUgPSBwcm9jZXNzZWROYW1lLnN1YnN0cmluZygwLCBwcm9jZXNzZWROYW1lLmxlbmd0aCAtIHN1ZmZpeC5sZW5ndGgpOwogICAgICAgICAgYnJlYWs7IC8vIOWPquWOu+aOieesrOS4gOS4quWMuemFjeeahOWQjue8gAogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIHByb2Nlc3NlZE5hbWU7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGludmVzdG1lbnRJZDogbnVsbCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBjb3ZlckltYWdlVXJsOiBudWxsLAogICAgICAgIGZpbmFuY2luZ1JvdW5kOiBudWxsLAogICAgICAgIGluZHVzdHJ5SWQ6IG51bGwsCiAgICAgICAgcmVnaW9uOiBudWxsLAogICAgICAgIHRhZ3M6IG51bGwsCiAgICAgICAgYnJpZWZJbnRyb2R1Y3Rpb246IG51bGwsCiAgICAgICAgZGV0YWlsQ29udGVudDogbnVsbCwKICAgICAgICB0b3BJbWFnZVVybDogbnVsbCwKICAgICAgICBjb250YWN0UGVyc29uOiBudWxsLAogICAgICAgIGNvbnRhY3RJbmZvOiBudWxsLAogICAgICAgIHZpZXdDb3VudDogMCwKICAgICAgICBzb3J0T3JkZXI6IDAsCiAgICAgICAgc3RhdHVzOiAiMCIKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaW52ZXN0bWVudElkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOmhueebruaKlei1hCI7CiAgICB9LAogICAgLyoqIOafpeeci+aMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpbnZlc3RtZW50SWQgPSByb3cuaW52ZXN0bWVudElkIHx8IHRoaXMuaWRzCiAgICAgIGdldEludmVzdG1lbnQoaW52ZXN0bWVudElkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLmn6XnnIvpobnnm67mipXotYTor6bmg4UiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgaW52ZXN0bWVudElkID0gcm93LmludmVzdG1lbnRJZCB8fCB0aGlzLmlkcwogICAgICBnZXRJbnZlc3RtZW50KGludmVzdG1lbnRJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56aG555uu5oqV6LWEIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaW52ZXN0bWVudElkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlSW52ZXN0bWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZEludmVzdG1lbnQodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGludmVzdG1lbnRJZHMgPSByb3cuaW52ZXN0bWVudElkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpobnnm67mipXotYTnvJblj7fkuLoiJyArIGludmVzdG1lbnRJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbEludmVzdG1lbnQoaW52ZXN0bWVudElkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKCdtaW5pYXBwL2ludmVzdG1lbnQvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYGludmVzdG1lbnRfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9CiAgfQp9Owo="}, null]}