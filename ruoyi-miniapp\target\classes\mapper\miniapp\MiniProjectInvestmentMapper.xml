<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniProjectInvestmentMapper">
    
    <resultMap type="MiniProjectInvestment" id="MiniProjectInvestmentResult">
        <result property="investmentId"    column="investment_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="financingRound"    column="financing_round"    />
        <result property="industryId"    column="industry_id"    />
        <result property="region"    column="region"    />
        <result property="tags"    column="tags"    />
        <result property="briefIntroduction"    column="brief_introduction"    />
        <result property="detailContent"    column="detail_content"    />
        <result property="topImageUrl"    column="top_image_url"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="viewCount"    column="view_count"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="industryName"    column="industry_name"    />
    </resultMap>

    <sql id="selectMiniProjectInvestmentVo">
        select pi.investment_id, pi.project_name, pi.cover_image_url, pi.financing_round, pi.industry_id, pi.region, pi.tags, pi.brief_introduction, pi.detail_content, pi.top_image_url, pi.contact_person, pi.contact_info, pi.view_count, pi.sort_order, pi.status, pi.create_by, pi.create_time, pi.update_by, pi.update_time, pi.remark,
               it.node_name as industry_name
        from mini_project_investment pi
        left join mini_industry_tree it on pi.industry_id = it.id
    </sql>

    <select id="selectMiniProjectInvestmentList" parameterType="MiniProjectInvestment" resultMap="MiniProjectInvestmentResult">
        <include refid="selectMiniProjectInvestmentVo"/>
        <where>  
            <if test="projectName != null  and projectName != ''"> and pi.project_name like concat('%', #{projectName}, '%')</if>
            <if test="financingRound != null  and financingRound != ''"> and pi.financing_round = #{financingRound}</if>
            <if test="industryId != null "> and pi.industry_id = #{industryId}</if>
            <if test="region != null  and region != ''"> and pi.region like concat('%', #{region}, '%')</if>
            <if test="tags != null  and tags != ''"> and pi.tags like concat('%', #{tags}, '%')</if>
            <if test="status != null  and status != ''"> and pi.status = #{status}</if>
        </where>
        order by pi.sort_order asc, pi.create_time desc
    </select>
    
    <select id="selectMiniProjectInvestmentByInvestmentId" parameterType="Long" resultMap="MiniProjectInvestmentResult">
        <include refid="selectMiniProjectInvestmentVo"/>
        where pi.investment_id = #{investmentId}
    </select>
        
    <insert id="insertMiniProjectInvestment" parameterType="MiniProjectInvestment" useGeneratedKeys="true" keyProperty="investmentId">
        insert into mini_project_investment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="coverImageUrl != null and coverImageUrl != ''">cover_image_url,</if>
            <if test="financingRound != null and financingRound != ''">financing_round,</if>
            <if test="industryId != null">industry_id,</if>
            <if test="region != null and region != ''">region,</if>
            <if test="tags != null">tags,</if>
            <if test="briefIntroduction != null">brief_introduction,</if>
            <if test="detailContent != null">detail_content,</if>
            <if test="topImageUrl != null">top_image_url,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="coverImageUrl != null and coverImageUrl != ''">#{coverImageUrl},</if>
            <if test="financingRound != null and financingRound != ''">#{financingRound},</if>
            <if test="industryId != null">#{industryId},</if>
            <if test="region != null and region != ''">#{region},</if>
            <if test="tags != null">#{tags},</if>
            <if test="briefIntroduction != null">#{briefIntroduction},</if>
            <if test="detailContent != null">#{detailContent},</if>
            <if test="topImageUrl != null">#{topImageUrl},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMiniProjectInvestment" parameterType="MiniProjectInvestment">
        update mini_project_investment
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="financingRound != null and financingRound != ''">financing_round = #{financingRound},</if>
            <if test="industryId != null">industry_id = #{industryId},</if>
            <if test="region != null and region != ''">region = #{region},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="briefIntroduction != null">brief_introduction = #{briefIntroduction},</if>
            <if test="detailContent != null">detail_content = #{detailContent},</if>
            <if test="topImageUrl != null">top_image_url = #{topImageUrl},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where investment_id = #{investmentId}
    </update>

    <delete id="deleteMiniProjectInvestmentByInvestmentId" parameterType="Long">
        delete from mini_project_investment where investment_id = #{investmentId}
    </delete>

    <delete id="deleteMiniProjectInvestmentByInvestmentIds" parameterType="String">
        delete from mini_project_investment where investment_id in 
        <foreach item="investmentId" collection="array" open="(" separator="," close=")">
            #{investmentId}
        </foreach>
    </delete>

    <!-- 查询启用的项目投资列表（小程序端调用） -->
    <select id="selectEnabledMiniProjectInvestmentList" resultMap="MiniProjectInvestmentResult">
        <include refid="selectMiniProjectInvestmentVo"/>
        where pi.status = '0'
        order by pi.sort_order asc, pi.create_time desc
    </select>

    <!-- 查询启用的项目投资列表（带筛选条件） -->
    <select id="selectEnabledMiniProjectInvestmentListWithFilter" resultMap="MiniProjectInvestmentResult">
        <include refid="selectMiniProjectInvestmentVo"/>
        <where>
            pi.status = '0'
            <if test="miniProjectInvestment.financingRound != null and miniProjectInvestment.financingRound != ''">
                and pi.financing_round = #{miniProjectInvestment.financingRound}
            </if>
            <if test="miniProjectInvestment.region != null and miniProjectInvestment.region != ''">
                and pi.region = #{miniProjectInvestment.region}
            </if>
            <if test="miniProjectInvestment.industryId != null">
                and pi.industry_id = #{miniProjectInvestment.industryId}
            </if>
            <if test="keyword != null and keyword != ''">
                and (pi.project_name like concat('%', #{keyword}, '%')
                     or pi.brief_introduction like concat('%', #{keyword}, '%')
                     or pi.tags like concat('%', #{keyword}, '%'))
            </if>
        </where>
        order by pi.sort_order asc, pi.create_time desc
    </select>

    <!-- 查询推荐的项目投资列表 -->
    <select id="selectRecommendedMiniProjectInvestmentList" resultMap="MiniProjectInvestmentResult">
        <include refid="selectMiniProjectInvestmentVo"/>
        where pi.status = '0'
        order by pi.view_count desc, pi.sort_order asc
        limit 10
    </select>

    <!-- 增加项目投资浏览次数 -->
    <update id="incrementViewCount" parameterType="Long">
        update mini_project_investment set view_count = view_count + 1 where investment_id = #{investmentId}
    </update>

    <!-- 根据行业ID查询项目投资列表 -->
    <select id="selectMiniProjectInvestmentByIndustryId" parameterType="Long" resultMap="MiniProjectInvestmentResult">
        <include refid="selectMiniProjectInvestmentVo"/>
        where pi.industry_id = #{industryId} and pi.status = '0'
        order by pi.sort_order asc, pi.create_time desc
    </select>

    <!-- 根据融资轮次查询项目投资列表 -->
    <select id="selectMiniProjectInvestmentByFinancingRound" parameterType="String" resultMap="MiniProjectInvestmentResult">
        <include refid="selectMiniProjectInvestmentVo"/>
        where pi.financing_round = #{financingRound} and pi.status = '0'
        order by pi.sort_order asc, pi.create_time desc
    </select>

    <!-- 根据地区查询项目投资列表 -->
    <select id="selectMiniProjectInvestmentByRegion" parameterType="String" resultMap="MiniProjectInvestmentResult">
        <include refid="selectMiniProjectInvestmentVo"/>
        where pi.region like concat('%', #{region}, '%') and pi.status = '0'
        order by pi.sort_order asc, pi.create_time desc
    </select>

</mapper>
