{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\investment.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\investment.js", "mtime": 1754102859759}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listInvestment", "query", "request", "url", "method", "params", "getInvestment", "investmentId", "addInvestment", "data", "updateInvestment", "delInvestment", "listEnabledInvestment", "listRecommendedInvestment", "incrementViewCount", "listInvestmentByIndustry", "industryId", "listInvestmentByFinancing", "financingRound", "listInvestmentByRegion", "region", "listIndustryTree"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/investment.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询项目投资列表\nexport function listInvestment(query) {\n  return request({\n    url: '/miniapp/investment/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询项目投资详细\nexport function getInvestment(investmentId) {\n  return request({\n    url: '/miniapp/investment/' + investmentId,\n    method: 'get'\n  })\n}\n\n// 新增项目投资\nexport function addInvestment(data) {\n  return request({\n    url: '/miniapp/investment',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改项目投资\nexport function updateInvestment(data) {\n  return request({\n    url: '/miniapp/investment',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除项目投资\nexport function delInvestment(investmentId) {\n  return request({\n    url: '/miniapp/investment/' + investmentId,\n    method: 'delete'\n  })\n}\n\n// 查询启用的项目投资列表（小程序端调用）\nexport function listEnabledInvestment(params) {\n  return request({\n    url: '/miniapp/investment/enabled',\n    method: 'get',\n    params: params\n  })\n}\n\n// 查询推荐的项目投资列表\nexport function listRecommendedInvestment() {\n  return request({\n    url: '/miniapp/investment/recommended',\n    method: 'get'\n  })\n}\n\n// 增加项目投资浏览次数\nexport function incrementViewCount(investmentId) {\n  return request({\n    url: '/miniapp/investment/view/' + investmentId,\n    method: 'post'\n  })\n}\n\n// 根据行业ID查询项目投资列表\nexport function listInvestmentByIndustry(industryId) {\n  return request({\n    url: '/miniapp/investment/industry/' + industryId,\n    method: 'get'\n  })\n}\n\n// 根据融资轮次查询项目投资列表\nexport function listInvestmentByFinancing(financingRound) {\n  return request({\n    url: '/miniapp/investment/financing/' + financingRound,\n    method: 'get'\n  })\n}\n\n// 根据地区查询项目投资列表\nexport function listInvestmentByRegion(region) {\n  return request({\n    url: '/miniapp/investment/region/' + region,\n    method: 'get'\n  })\n}\n\n// 查询行业树列表\nexport function listIndustryTree() {\n  return request({\n    url: '/miniapp/industry/tree',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,YAAY,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,YAAY;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,YAAY,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,YAAY;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,qBAAqBA,CAACP,MAAM,EAAE;EAC5C,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,yBAAyBA,CAAA,EAAG;EAC1C,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,kBAAkBA,CAACP,YAAY,EAAE;EAC/C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGI,YAAY;IAC/CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,wBAAwBA,CAACC,UAAU,EAAE;EACnD,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B,GAAGa,UAAU;IACjDZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,yBAAyBA,CAACC,cAAc,EAAE;EACxD,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGe,cAAc;IACtDd,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,OAAO,IAAAlB,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B,GAAGiB,MAAM;IAC3ChB,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASiB,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAnB,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}