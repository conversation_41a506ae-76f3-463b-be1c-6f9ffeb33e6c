{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\index.vue", "mtime": 1754103601119}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}