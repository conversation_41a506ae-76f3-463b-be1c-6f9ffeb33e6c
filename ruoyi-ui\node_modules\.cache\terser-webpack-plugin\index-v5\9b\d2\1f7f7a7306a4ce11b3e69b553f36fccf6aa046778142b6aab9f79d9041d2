
570e2549fc89b6475c386d1333f77ae9170eb017	{"key":"{\"terser\":\"4.8.1\",\"terser-webpack-plugin\":\"2.3.8\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":false,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true},\"mangle\":{\"safari10\":true}}},\"nodeVersion\":\"v22.15.1\",\"filename\":\"static\\u002Fjs\\u002Fruntime.564ada8e.js\",\"contentHash\":\"a5d1b0b4f2ab88feaeea\"}","integrity":"sha512-o48mmHvWf31Byj2tiAkPw02GhVASI0pV0WGQLamoyod89Xm6ioo5y9lnP1ekvffal4441VuFJkOZA6ZPJHJoYQ==","time":1754054201628,"size":9958}