{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-2d21af45\"],{be5a:function(e,t,l){\"use strict\";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l(\"div\",{staticClass:\"app-container\"},[l(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0}},[l(\"el-form-item\",{attrs:{label:\"项目名称\",prop:\"projectName\"}},[l(\"el-input\",{attrs:{placeholder:\"请输入项目名称\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.projectName,callback:function(t){e.$set(e.queryParams,\"projectName\",t)},expression:\"queryParams.projectName\"}})],1),l(\"el-form-item\",{attrs:{label:\"融资轮次\",prop:\"financingRound\"}},[l(\"el-select\",{attrs:{placeholder:\"请选择融资轮次\",clearable:\"\"},model:{value:e.queryParams.financingRound,callback:function(t){e.$set(e.queryParams,\"financingRound\",t)},expression:\"queryParams.financingRound\"}},[l(\"el-option\",{attrs:{label:\"种子轮\",value:\"种子轮\"}}),l(\"el-option\",{attrs:{label:\"天使轮\",value:\"天使轮\"}}),l(\"el-option\",{attrs:{label:\"Pre-A轮\",value:\"Pre-A轮\"}}),l(\"el-option\",{attrs:{label:\"A轮\",value:\"A轮\"}}),l(\"el-option\",{attrs:{label:\"A+轮\",value:\"A+轮\"}}),l(\"el-option\",{attrs:{label:\"B轮\",value:\"B轮\"}}),l(\"el-option\",{attrs:{label:\"B+轮\",value:\"B+轮\"}}),l(\"el-option\",{attrs:{label:\"C轮\",value:\"C轮\"}}),l(\"el-option\",{attrs:{label:\"C+轮\",value:\"C+轮\"}}),l(\"el-option\",{attrs:{label:\"D轮\",value:\"D轮\"}}),l(\"el-option\",{attrs:{label:\"E轮\",value:\"E轮\"}}),l(\"el-option\",{attrs:{label:\"F轮\",value:\"F轮\"}}),l(\"el-option\",{attrs:{label:\"IPO\",value:\"IPO\"}})],1)],1),l(\"el-form-item\",{attrs:{label:\"所在地区\",prop:\"region\"}},[l(\"el-select\",{attrs:{placeholder:\"请选择所在地区\",clearable:\"\",filterable:\"\"},model:{value:e.queryParams.region,callback:function(t){e.$set(e.queryParams,\"region\",t)},expression:\"queryParams.region\"}},e._l(e.provinceOptions,(function(e){return l(\"el-option\",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),l(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[l(\"el-select\",{attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,\"status\",t)},expression:\"queryParams.status\"}},e._l(e.dict.type.sys_normal_disable,(function(e){return l(\"el-option\",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),l(\"el-form-item\",[l(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),l(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),l(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[l(\"el-col\",{attrs:{span:1.5}},[l(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:investment:add\"],expression:\"['miniapp:investment:add']\"}],attrs:{type:\"primary\",plain:\"\",icon:\"el-icon-plus\",size:\"mini\"},on:{click:e.handleAdd}},[e._v(\"新增\")])],1),l(\"el-col\",{attrs:{span:1.5}},[l(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:investment:edit\"],expression:\"['miniapp:investment:edit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-edit\",size:\"mini\",disabled:e.single},on:{click:e.handleUpdate}},[e._v(\"修改\")])],1),l(\"el-col\",{attrs:{span:1.5}},[l(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:investment:remove\"],expression:\"['miniapp:investment:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(\"删除\")])],1),l(\"el-col\",{attrs:{span:1.5}},[l(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:investment:export\"],expression:\"['miniapp:investment:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),l(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),l(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.investmentList},on:{\"selection-change\":e.handleSelectionChange}},[l(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),l(\"el-table-column\",{attrs:{label:\"项目ID\",align:\"center\",prop:\"investmentId\",width:\"80\"}}),l(\"el-table-column\",{attrs:{label:\"项目名称\",align:\"center\",prop:\"projectName\",\"min-width\":\"180\",\"show-overflow-tooltip\":\"\"}}),l(\"el-table-column\",{attrs:{label:\"封面图片\",align:\"center\",prop:\"coverImageUrl\",width:\"120\"},scopedSlots:e._u([{key:\"default\",fn:function(e){return[l(\"image-preview\",{attrs:{src:e.row.coverImageUrl,width:80,height:50}})]}}])}),l(\"el-table-column\",{attrs:{label:\"融资轮次\",align:\"center\",prop:\"financingRound\",width:\"110\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[l(\"el-tag\",{attrs:{type:\"success\"}},[e._v(e._s(t.row.financingRound))])]}}])}),l(\"el-table-column\",{attrs:{label:\"所属行业\",align:\"center\",prop:\"industryName\",\"min-width\":\"120\",\"show-overflow-tooltip\":\"\"}}),l(\"el-table-column\",{attrs:{label:\"所在地区\",align:\"center\",prop:\"region\",width:\"100\"}}),l(\"el-table-column\",{attrs:{label:\"项目标签\",align:\"center\",prop:\"tags\",\"min-width\":\"250\",\"show-overflow-tooltip\":\"\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return e._l(e.getTagArray(t.row.tags),(function(t,a){return l(\"el-tag\",{key:a,staticStyle:{\"margin-right\":\"5px\",\"margin-bottom\":\"3px\"},attrs:{size:\"mini\",type:\"info\"}},[e._v(\" \"+e._s(t)+\" \")])}))}}])}),l(\"el-table-column\",{attrs:{label:\"简介\",align:\"center\",prop:\"briefIntroduction\",\"min-width\":\"200\",\"show-overflow-tooltip\":\"\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[l(\"span\",[e._v(e._s(t.row.briefIntroduction))])]}}])}),l(\"el-table-column\",{attrs:{label:\"联系人\",align:\"center\",prop:\"contactPerson\",width:\"100\"}}),l(\"el-table-column\",{attrs:{label:\"浏览次数\",align:\"center\",prop:\"viewCount\",width:\"90\"}}),l(\"el-table-column\",{attrs:{label:\"排序\",align:\"center\",prop:\"sortOrder\",width:\"70\"}}),l(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",prop:\"status\",width:\"80\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[l(\"dict-tag\",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),l(\"el-table-column\",{attrs:{label:\"创建时间\",align:\"center\",prop:\"createTime\",width:\"160\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[l(\"span\",[e._v(e._s(e.parseTime(t.row.createTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]}}])}),l(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\",width:\"180\",fixed:\"right\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[l(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:investment:query\"],expression:\"['miniapp:investment:query']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-view\"},on:{click:function(l){return e.handleView(t.row)}}},[e._v(\"查看\")]),l(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:investment:edit\"],expression:\"['miniapp:investment:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-edit\"},on:{click:function(l){return e.handleUpdate(t.row)}}},[e._v(\"修改\")]),l(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:investment:remove\"],expression:\"['miniapp:investment:remove']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(l){return e.handleDelete(t.row)}}},[e._v(\"删除\")])]}}])})],1),l(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total>0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),l(\"el-dialog\",{attrs:{title:e.title,visible:e.open,width:\"800px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.open=t}}},[l(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"120px\"}},[l(\"el-row\",[l(\"el-col\",{attrs:{span:12}},[l(\"el-form-item\",{attrs:{label:\"项目名称\",prop:\"projectName\"}},[l(\"el-input\",{attrs:{placeholder:\"请输入项目名称\"},model:{value:e.form.projectName,callback:function(t){e.$set(e.form,\"projectName\",t)},expression:\"form.projectName\"}})],1)],1),l(\"el-col\",{attrs:{span:12}},[l(\"el-form-item\",{attrs:{label:\"融资轮次\",prop:\"financingRound\"}},[l(\"el-select\",{attrs:{placeholder:\"请选择融资轮次\"},model:{value:e.form.financingRound,callback:function(t){e.$set(e.form,\"financingRound\",t)},expression:\"form.financingRound\"}},[l(\"el-option\",{attrs:{label:\"种子轮\",value:\"种子轮\"}}),l(\"el-option\",{attrs:{label:\"天使轮\",value:\"天使轮\"}}),l(\"el-option\",{attrs:{label:\"Pre-A轮\",value:\"Pre-A轮\"}}),l(\"el-option\",{attrs:{label:\"A轮\",value:\"A轮\"}}),l(\"el-option\",{attrs:{label:\"A+轮\",value:\"A+轮\"}}),l(\"el-option\",{attrs:{label:\"B轮\",value:\"B轮\"}}),l(\"el-option\",{attrs:{label:\"B+轮\",value:\"B+轮\"}}),l(\"el-option\",{attrs:{label:\"C轮\",value:\"C轮\"}}),l(\"el-option\",{attrs:{label:\"C+轮\",value:\"C+轮\"}}),l(\"el-option\",{attrs:{label:\"D轮\",value:\"D轮\"}}),l(\"el-option\",{attrs:{label:\"E轮\",value:\"E轮\"}}),l(\"el-option\",{attrs:{label:\"F轮\",value:\"F轮\"}}),l(\"el-option\",{attrs:{label:\"IPO\",value:\"IPO\"}})],1)],1)],1)],1),l(\"el-row\",[l(\"el-col\",{attrs:{span:12}},[l(\"el-form-item\",{attrs:{label:\"所属行业\",prop:\"industryId\"}},[l(\"el-select\",{attrs:{placeholder:\"请选择所属行业\",filterable:\"\"},model:{value:e.form.industryId,callback:function(t){e.$set(e.form,\"industryId\",t)},expression:\"form.industryId\"}},e._l(e.industryOptions,(function(e){return l(\"el-option\",{key:e.id,attrs:{label:e.nodeName,value:e.id}})})),1)],1)],1),l(\"el-col\",{attrs:{span:12}},[l(\"el-form-item\",{attrs:{label:\"所在地区\",prop:\"region\"}},[l(\"el-select\",{attrs:{placeholder:\"请选择所在地区\",filterable:\"\"},model:{value:e.form.region,callback:function(t){e.$set(e.form,\"region\",t)},expression:\"form.region\"}},e._l(e.provinceOptions,(function(e){return l(\"el-option\",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),l(\"el-row\",[l(\"el-col\",{attrs:{span:12}},[l(\"el-form-item\",{attrs:{label:\"联系人\",prop:\"contactPerson\"}},[l(\"el-input\",{attrs:{placeholder:\"请输入联系人姓名\"},model:{value:e.form.contactPerson,callback:function(t){e.$set(e.form,\"contactPerson\",t)},expression:\"form.contactPerson\"}})],1)],1),l(\"el-col\",{attrs:{span:12}},[l(\"el-form-item\",{attrs:{label:\"联系方式\",prop:\"contactInfo\"}},[l(\"el-input\",{attrs:{placeholder:\"请输入联系方式\"},model:{value:e.form.contactInfo,callback:function(t){e.$set(e.form,\"contactInfo\",t)},expression:\"form.contactInfo\"}})],1)],1)],1),l(\"el-row\",[l(\"el-col\",{attrs:{span:12}},[l(\"el-form-item\",{attrs:{label:\"排序\",prop:\"sortOrder\"}},[l(\"el-input-number\",{attrs:{\"controls-position\":\"right\",min:0},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,\"sortOrder\",t)},expression:\"form.sortOrder\"}})],1)],1),l(\"el-col\",{attrs:{span:12}},[l(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[l(\"el-radio-group\",{model:{value:e.form.status,callback:function(t){e.$set(e.form,\"status\",t)},expression:\"form.status\"}},e._l(e.dict.type.sys_normal_disable,(function(t){return l(\"el-radio\",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1)],1)],1),l(\"el-form-item\",{attrs:{label:\"项目标签\",prop:\"tags\"}},[l(\"el-input\",{attrs:{placeholder:\"请输入项目标签，多个标签用逗号分隔\"},model:{value:e.form.tags,callback:function(t){e.$set(e.form,\"tags\",t)},expression:\"form.tags\"}}),l(\"div\",{staticStyle:{\"margin-top\":\"5px\",color:\"#909399\",\"font-size\":\"12px\"}},[e._v(\" 示例：人工智能,大数据,云计算,物联网 \")])],1),l(\"el-form-item\",{attrs:{label:\"封面图片\",prop:\"coverImageUrl\"}},[l(\"image-upload\",{model:{value:e.form.coverImageUrl,callback:function(t){e.$set(e.form,\"coverImageUrl\",t)},expression:\"form.coverImageUrl\"}})],1),l(\"el-form-item\",{attrs:{label:\"详情顶图\",prop:\"topImageUrl\"}},[l(\"image-upload\",{model:{value:e.form.topImageUrl,callback:function(t){e.$set(e.form,\"topImageUrl\",t)},expression:\"form.topImageUrl\"}})],1),l(\"el-form-item\",{attrs:{label:\"项目简介\",prop:\"briefIntroduction\"}},[l(\"el-input\",{attrs:{type:\"textarea\",rows:3,placeholder:\"请输入项目简介\"},model:{value:e.form.briefIntroduction,callback:function(t){e.$set(e.form,\"briefIntroduction\",t)},expression:\"form.briefIntroduction\"}})],1),l(\"el-form-item\",{attrs:{label:\"项目详情\",prop:\"detailContent\"}},[l(\"editor\",{attrs:{\"min-height\":192},model:{value:e.form.detailContent,callback:function(t){e.$set(e.form,\"detailContent\",t)},expression:\"form.detailContent\"}})],1),l(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[l(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入内容\"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1),l(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[l(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitForm}},[e._v(\"确 定\")]),l(\"el-button\",{on:{click:e.cancel}},[e._v(\"取 消\")])],1)],1)],1)},n=[],r=l(\"5530\"),o=(l(\"4de4\"),l(\"d81d\"),l(\"d3b7\"),l(\"498a\"),l(\"0643\"),l(\"2382\"),l(\"a573\"),l(\"b775\"));function i(e){return Object(o[\"a\"])({url:\"/miniapp/investment/list\",method:\"get\",params:e})}function s(e){return Object(o[\"a\"])({url:\"/miniapp/investment/\"+e,method:\"get\"})}function u(e){return Object(o[\"a\"])({url:\"/miniapp/investment\",method:\"post\",data:e})}function c(e){return Object(o[\"a\"])({url:\"/miniapp/investment\",method:\"put\",data:e})}function m(e){return Object(o[\"a\"])({url:\"/miniapp/investment/\"+e,method:\"delete\"})}function p(){return Object(o[\"a\"])({url:\"/miniapp/industry/tree\",method:\"get\"})}var d={name:\"Investment\",dicts:[\"sys_normal_disable\"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,investmentList:[],industryOptions:[],provinceOptions:[{value:\"北京\",label:\"北京市\"},{value:\"天津\",label:\"天津市\"},{value:\"河北\",label:\"河北省\"},{value:\"山西\",label:\"山西省\"},{value:\"内蒙古\",label:\"内蒙古自治区\"},{value:\"辽宁\",label:\"辽宁省\"},{value:\"吉林\",label:\"吉林省\"},{value:\"黑龙江\",label:\"黑龙江省\"},{value:\"上海\",label:\"上海市\"},{value:\"江苏\",label:\"江苏省\"},{value:\"浙江\",label:\"浙江省\"},{value:\"安徽\",label:\"安徽省\"},{value:\"福建\",label:\"福建省\"},{value:\"江西\",label:\"江西省\"},{value:\"山东\",label:\"山东省\"},{value:\"河南\",label:\"河南省\"},{value:\"湖北\",label:\"湖北省\"},{value:\"湖南\",label:\"湖南省\"},{value:\"广东\",label:\"广东省\"},{value:\"广西\",label:\"广西壮族自治区\"},{value:\"海南\",label:\"海南省\"},{value:\"重庆\",label:\"重庆市\"},{value:\"四川\",label:\"四川省\"},{value:\"贵州\",label:\"贵州省\"},{value:\"云南\",label:\"云南省\"},{value:\"西藏\",label:\"西藏自治区\"},{value:\"陕西\",label:\"陕西省\"},{value:\"甘肃\",label:\"甘肃省\"},{value:\"青海\",label:\"青海省\"},{value:\"宁夏\",label:\"宁夏回族自治区\"},{value:\"新疆\",label:\"新疆维吾尔自治区\"},{value:\"台湾\",label:\"台湾省\"},{value:\"香港\",label:\"香港特别行政区\"},{value:\"澳门\",label:\"澳门特别行政区\"}],title:\"\",open:!1,queryParams:{pageNum:1,pageSize:10,projectName:null,financingRound:null,industryId:null,region:null,status:null},form:{},rules:{projectName:[{required:!0,message:\"项目名称不能为空\",trigger:\"blur\"}],financingRound:[{required:!0,message:\"融资轮次不能为空\",trigger:\"change\"}],industryId:[{required:!0,message:\"所属行业不能为空\",trigger:\"change\"}],region:[{required:!0,message:\"所在地区不能为空\",trigger:\"blur\"}]}}},created:function(){this.getList(),this.getIndustryTree()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.investmentList=t.rows,e.total=t.total,e.loading=!1}))},getIndustryTree:function(){var e=this;p().then((function(t){e.industryOptions=t.data}))},getTagArray:function(e){return e?e.split(\",\").filter((function(e){return\"\"!==e.trim()})):[]},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={investmentId:null,projectName:null,coverImageUrl:null,financingRound:null,industryId:null,region:null,tags:null,briefIntroduction:null,detailContent:null,topImageUrl:null,contactPerson:null,contactInfo:null,viewCount:0,sortOrder:0,status:\"0\"},this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.investmentId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=\"添加项目投资\"},handleView:function(e){var t=this;this.reset();var l=e.investmentId||this.ids;s(l).then((function(e){t.form=e.data,t.open=!0,t.title=\"查看项目投资详情\"}))},handleUpdate:function(e){var t=this;this.reset();var l=e.investmentId||this.ids;s(l).then((function(e){t.form=e.data,t.open=!0,t.title=\"修改项目投资\"}))},submitForm:function(){var e=this;this.$refs[\"form\"].validate((function(t){t&&(null!=e.form.investmentId?c(e.form).then((function(t){e.$modal.msgSuccess(\"修改成功\"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess(\"新增成功\"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,l=e.investmentId||this.ids;this.$modal.confirm('是否确认删除项目投资编号为\"'+l+'\"的数据项？').then((function(){return m(l)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/investment/export\",Object(r[\"a\"])({},this.queryParams),\"investment_\".concat((new Date).getTime(),\".xlsx\"))}}},f=d,v=l(\"2877\"),b=Object(v[\"a\"])(f,a,n,!1,null,null,null);t[\"default\"]=b.exports}}]);", "extractedComments": []}