# 项目投资筛选API测试文档

## 📋 接口信息

**接口地址**: `GET /miniapp/investment/enabled`

**接口描述**: 查询启用的项目投资列表（小程序端调用），支持多条件筛选

## 🔧 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| financingRound | String | 否 | 融资轮次 | "B轮融资" |
| region | String | 否 | 所在地区 | "上海" |
| industryId | Long | 否 | 行业ID | 30 |
| keyword | String | 否 | 关键字（项目名称、简介、标签） | "智能" |

## 🎯 测试用例

### 1. 查询所有启用项目
```http
GET /miniapp/investment/enabled
```

**预期结果**: 返回所有状态为启用的项目投资

### 2. 按融资轮次筛选
```http
GET /miniapp/investment/enabled?financingRound=B轮融资
```

**预期结果**: 返回融资轮次为"B轮融资"的项目

### 3. 按地区筛选
```http
GET /miniapp/investment/enabled?region=上海
```

**预期结果**: 返回所在地区为"上海"的项目

### 4. 按行业筛选
```http
GET /miniapp/investment/enabled?industryId=30
```

**预期结果**: 返回行业ID为30的项目

### 5. 按关键字搜索
```http
GET /miniapp/investment/enabled?keyword=智能
```

**预期结果**: 返回项目名称、简介或标签中包含"智能"的项目

### 6. 组合筛选
```http
GET /miniapp/investment/enabled?region=北京&financingRound=B轮融资&keyword=AI
```

**预期结果**: 返回同时满足地区为北京、融资轮次为B轮、包含AI关键字的项目

## 📊 测试数据

当前数据库中的测试数据：

| 项目名称 | 融资轮次 | 地区 | 行业 | 标签 |
|----------|----------|------|------|------|
| 智能制造科技 | A轮融资 | 上海 | 航空航天 | 智能制造,工业4.0,自动化,物联网 |
| 绿色能源科技 | 天使轮 | 深圳 | 航空航天 | 新能源,太阳能,风能,储能 |
| 医疗AI助手 | B轮融资 | 北京 | 航空航天 | 人工智能,医疗,大数据,机器学习 |
| 云遥宇航 | B轮 | 天津 | 硬科技 | 航空航天 |

## 🔍 筛选逻辑

### 关键字搜索范围
- **项目名称**: 模糊匹配项目名称
- **项目简介**: 模糊匹配项目简介内容
- **项目标签**: 模糊匹配标签内容

### 筛选条件组合
- 所有筛选条件之间为 **AND** 关系
- 关键字在项目名称、简介、标签之间为 **OR** 关系
- 空参数会被忽略，不参与筛选

## 📱 前端使用示例

```javascript
import { listEnabledInvestment } from "@/api/miniapp/investment";

// 查询所有项目
listEnabledInvestment().then(response => {
  console.log('所有项目:', response.data);
});

// 按条件筛选
const params = {
  financingRound: 'B轮融资',
  region: '北京',
  keyword: 'AI'
};

listEnabledInvestment(params).then(response => {
  console.log('筛选结果:', response.data);
});
```

## ✅ 验证要点

1. **参数验证**: 确保空参数不影响查询结果
2. **模糊搜索**: 关键字能正确匹配项目名称、简介、标签
3. **精确匹配**: 融资轮次、地区、行业ID为精确匹配
4. **组合筛选**: 多个条件同时生效
5. **排序规则**: 按排序字段升序，创建时间降序
6. **状态过滤**: 只返回启用状态(status='0')的项目

## 🎨 响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "investmentId": 5,
      "projectName": "智能制造科技",
      "coverImageUrl": "https://example.com/images/smart-manufacturing-cover.jpg",
      "financingRound": "A轮融资",
      "industryId": 30,
      "industryName": "航空航天",
      "region": "上海",
      "tags": "智能制造,工业4.0,自动化,物联网",
      "briefIntroduction": "专注于智能制造解决方案...",
      "contactPerson": "李总",
      "contactInfo": "<EMAIL>",
      "viewCount": 0,
      "status": "0"
    }
  ]
}
```

## 🚀 性能优化建议

1. **索引优化**: 为常用筛选字段添加数据库索引
2. **缓存策略**: 对热门筛选条件结果进行缓存
3. **分页支持**: 大数据量时考虑添加分页参数
4. **搜索优化**: 考虑使用全文搜索引擎优化关键字搜索
