{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\index.vue", "mtime": 1754103601119}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RJbnZlc3RtZW50LCBnZXRJbnZlc3RtZW50LCBkZWxJbnZlc3RtZW50LCBhZGRJbnZlc3RtZW50LCB1cGRhdGVJbnZlc3RtZW50IH0gZnJvbSAiQC9hcGkvbWluaWFwcC9pbnZlc3RtZW50IjsKaW1wb3J0IHsgbGlzdEluZHVzdHJ5VHJlZSB9IGZyb20gIkAvYXBpL21pbmlhcHAvaW52ZXN0bWVudCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkludmVzdG1lbnQiLAogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6aG555uu5oqV6LWE6KGo5qC85pWw5o2uCiAgICAgIGludmVzdG1lbnRMaXN0OiBbXSwKICAgICAgLy8g6KGM5Lia5qCR6YCJ6aG5CiAgICAgIGluZHVzdHJ5T3B0aW9uczogW10sCiAgICAgIC8vIOecgeS7vemAiemhuSAtIOebtOaOpeS9v+eUqOeugOensAogICAgICBwcm92aW5jZU9wdGlvbnM6IFsKICAgICAgICB7IHZhbHVlOiAn5YyX5LqsJywgbGFiZWw6ICfljJfkuqwnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+Wkqea0pScsIGxhYmVsOiAn5aSp5rSlJyB9LAogICAgICAgIHsgdmFsdWU6ICfmsrPljJcnLCBsYWJlbDogJ+ays+WMlycgfSwKICAgICAgICB7IHZhbHVlOiAn5bGx6KW/JywgbGFiZWw6ICflsbHopb8nIH0sCiAgICAgICAgeyB2YWx1ZTogJ+WGheiSmeWPpCcsIGxhYmVsOiAn5YaF6JKZ5Y+kJyB9LAogICAgICAgIHsgdmFsdWU6ICfovr3lroEnLCBsYWJlbDogJ+i+veWugScgfSwKICAgICAgICB7IHZhbHVlOiAn5ZCJ5p6XJywgbGFiZWw6ICflkInmnpcnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+m7kem+meaxnycsIGxhYmVsOiAn6buR6b6Z5rGfJyB9LAogICAgICAgIHsgdmFsdWU6ICfkuIrmtbcnLCBsYWJlbDogJ+S4iua1tycgfSwKICAgICAgICB7IHZhbHVlOiAn5rGf6IuPJywgbGFiZWw6ICfmsZ/oi48nIH0sCiAgICAgICAgeyB2YWx1ZTogJ+a1meaxnycsIGxhYmVsOiAn5rWZ5rGfJyB9LAogICAgICAgIHsgdmFsdWU6ICflronlvr0nLCBsYWJlbDogJ+WuieW+vScgfSwKICAgICAgICB7IHZhbHVlOiAn56aP5bu6JywgbGFiZWw6ICfnpo/lu7onIH0sCiAgICAgICAgeyB2YWx1ZTogJ+axn+ilvycsIGxhYmVsOiAn5rGf6KW/JyB9LAogICAgICAgIHsgdmFsdWU6ICflsbHkuJwnLCBsYWJlbDogJ+WxseS4nCcgfSwKICAgICAgICB7IHZhbHVlOiAn5rKz5Y2XJywgbGFiZWw6ICfmsrPljZcnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+a5luWMlycsIGxhYmVsOiAn5rmW5YyXJyB9LAogICAgICAgIHsgdmFsdWU6ICfmuZbljZcnLCBsYWJlbDogJ+a5luWNlycgfSwKICAgICAgICB7IHZhbHVlOiAn5bm/5LicJywgbGFiZWw6ICflub/kuJwnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+W5v+ilvycsIGxhYmVsOiAn5bm/6KW/JyB9LAogICAgICAgIHsgdmFsdWU6ICfmtbfljZcnLCBsYWJlbDogJ+a1t+WNlycgfSwKICAgICAgICB7IHZhbHVlOiAn6YeN5bqGJywgbGFiZWw6ICfph43luoYnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+Wbm+W3nScsIGxhYmVsOiAn5Zub5bedJyB9LAogICAgICAgIHsgdmFsdWU6ICfotLXlt54nLCBsYWJlbDogJ+i0teW3nicgfSwKICAgICAgICB7IHZhbHVlOiAn5LqR5Y2XJywgbGFiZWw6ICfkupHljZcnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+ilv+iXjycsIGxhYmVsOiAn6KW/6JePJyB9LAogICAgICAgIHsgdmFsdWU6ICfpmZXopb8nLCBsYWJlbDogJ+mZleilvycgfSwKICAgICAgICB7IHZhbHVlOiAn55SY6IKDJywgbGFiZWw6ICfnlJjogoMnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+mdkua1tycsIGxhYmVsOiAn6Z2S5rW3JyB9LAogICAgICAgIHsgdmFsdWU6ICflroHlpI8nLCBsYWJlbDogJ+WugeWkjycgfSwKICAgICAgICB7IHZhbHVlOiAn5paw55aGJywgbGFiZWw6ICfmlrDnloYnIH0sCiAgICAgICAgeyB2YWx1ZTogJ+WPsOa5vicsIGxhYmVsOiAn5Y+w5rm+JyB9LAogICAgICAgIHsgdmFsdWU6ICfpppnmuK8nLCBsYWJlbDogJ+mmmea4rycgfSwKICAgICAgICB7IHZhbHVlOiAn5r6z6ZeoJywgbGFiZWw6ICfmvrPpl6gnIH0KICAgICAgXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBmaW5hbmNpbmdSb3VuZDogbnVsbCwKICAgICAgICBpbmR1c3RyeUlkOiBudWxsLAogICAgICAgIHJlZ2lvbjogbnVsbCwKICAgICAgICBzdGF0dXM6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBwcm9qZWN0TmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumhueebruWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBmaW5hbmNpbmdSb3VuZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiejei1hOi9ruasoeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIGluZHVzdHJ5SWQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7ooYzkuJrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICByZWdpb246IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlnKjlnLDljLrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0SW5kdXN0cnlUcmVlKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6aG555uu5oqV6LWE5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0SW52ZXN0bWVudCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmludmVzdG1lbnRMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6Xor6LooYzkuJrmoJEgKi8KICAgIGdldEluZHVzdHJ5VHJlZSgpIHsKICAgICAgbGlzdEluZHVzdHJ5VHJlZSgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuaW5kdXN0cnlPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWkhOeQhuagh+etvuaVsOe7hCAqLwogICAgZ2V0VGFnQXJyYXkodGFncykgewogICAgICBpZiAoIXRhZ3MpIHJldHVybiBbXTsKICAgICAgcmV0dXJuIHRhZ3Muc3BsaXQoJywnKS5maWx0ZXIodGFnID0+IHRhZy50cmltKCkgIT09ICcnKTsKICAgIH0sCiAgICAvKiog5aSE55CG5Zyw5Yy65ZCN56ew77yM5Y675o6J55yB5biC6Ieq5rK75Yy6562J5ZCO57yAICovCiAgICBwcm9jZXNzUmVnaW9uTmFtZShyZWdpb25OYW1lKSB7CiAgICAgIGlmICghcmVnaW9uTmFtZSkgcmV0dXJuIHJlZ2lvbk5hbWU7CgogICAgICAvLyDlrprkuYnpnIDopoHljrvmjonnmoTlkI7nvIAKICAgICAgY29uc3Qgc3VmZml4ZXMgPSBbJ+ecgScsICfluIInLCAn6Ieq5rK75Yy6JywgJ+eJueWIq+ihjOaUv+WMuicsICflo67ml4/oh6rmsrvljLonLCAn5Zue5peP6Ieq5rK75Yy6JywgJ+e7tOWQvuWwlOiHquayu+WMuiddOwoKICAgICAgbGV0IHByb2Nlc3NlZE5hbWUgPSByZWdpb25OYW1lOwogICAgICBmb3IgKGNvbnN0IHN1ZmZpeCBvZiBzdWZmaXhlcykgewogICAgICAgIGlmIChwcm9jZXNzZWROYW1lLmVuZHNXaXRoKHN1ZmZpeCkpIHsKICAgICAgICAgIHByb2Nlc3NlZE5hbWUgPSBwcm9jZXNzZWROYW1lLnN1YnN0cmluZygwLCBwcm9jZXNzZWROYW1lLmxlbmd0aCAtIHN1ZmZpeC5sZW5ndGgpOwogICAgICAgICAgYnJlYWs7IC8vIOWPquWOu+aOieesrOS4gOS4quWMuemFjeeahOWQjue8gAogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIHByb2Nlc3NlZE5hbWU7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGludmVzdG1lbnRJZDogbnVsbCwKICAgICAgICBwcm9qZWN0TmFtZTogbnVsbCwKICAgICAgICBjb3ZlckltYWdlVXJsOiBudWxsLAogICAgICAgIGZpbmFuY2luZ1JvdW5kOiBudWxsLAogICAgICAgIGluZHVzdHJ5SWQ6IG51bGwsCiAgICAgICAgcmVnaW9uOiBudWxsLAogICAgICAgIHRhZ3M6IG51bGwsCiAgICAgICAgYnJpZWZJbnRyb2R1Y3Rpb246IG51bGwsCiAgICAgICAgZGV0YWlsQ29udGVudDogbnVsbCwKICAgICAgICB0b3BJbWFnZVVybDogbnVsbCwKICAgICAgICBjb250YWN0UGVyc29uOiBudWxsLAogICAgICAgIGNvbnRhY3RJbmZvOiBudWxsLAogICAgICAgIHZpZXdDb3VudDogMCwKICAgICAgICBzb3J0T3JkZXI6IDAsCiAgICAgICAgc3RhdHVzOiAiMCIKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaW52ZXN0bWVudElkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOmhueebruaKlei1hCI7CiAgICB9LAogICAgLyoqIOafpeeci+aMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVmlldyhyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpbnZlc3RtZW50SWQgPSByb3cuaW52ZXN0bWVudElkIHx8IHRoaXMuaWRzCiAgICAgIGdldEludmVzdG1lbnQoaW52ZXN0bWVudElkKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLmn6XnnIvpobnnm67mipXotYTor6bmg4UiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgY29uc3QgaW52ZXN0bWVudElkID0gcm93LmludmVzdG1lbnRJZCB8fCB0aGlzLmlkcwogICAgICBnZXRJbnZlc3RtZW50KGludmVzdG1lbnRJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56aG555uu5oqV6LWEIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaW52ZXN0bWVudElkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlSW52ZXN0bWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZEludmVzdG1lbnQodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGludmVzdG1lbnRJZHMgPSByb3cuaW52ZXN0bWVudElkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpobnnm67mipXotYTnvJblj7fkuLoiJyArIGludmVzdG1lbnRJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbEludmVzdG1lbnQoaW52ZXN0bWVudElkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICB0aGlzLmRvd25sb2FkKCdtaW5pYXBwL2ludmVzdG1lbnQvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYGludmVzdG1lbnRfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8SA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/business/investment", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\n        <el-input\n          v-model=\"queryParams.projectName\"\n          placeholder=\"请输入项目名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"融资轮次\" prop=\"financingRound\">\n        <el-select v-model=\"queryParams.financingRound\" placeholder=\"请选择融资轮次\" clearable>\n          <el-option label=\"种子轮\" value=\"种子轮\" />\n          <el-option label=\"天使轮\" value=\"天使轮\" />\n          <el-option label=\"Pre-A轮\" value=\"Pre-A轮\" />\n          <el-option label=\"A轮\" value=\"A轮\" />\n          <el-option label=\"A+轮\" value=\"A+轮\" />\n          <el-option label=\"B轮\" value=\"B轮\" />\n          <el-option label=\"B+轮\" value=\"B+轮\" />\n          <el-option label=\"C轮\" value=\"C轮\" />\n          <el-option label=\"C+轮\" value=\"C+轮\" />\n          <el-option label=\"D轮\" value=\"D轮\" />\n          <el-option label=\"E轮\" value=\"E轮\" />\n          <el-option label=\"F轮\" value=\"F轮\" />\n          <el-option label=\"IPO\" value=\"IPO\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"所在地区\" prop=\"region\">\n        <el-select v-model=\"queryParams.region\" placeholder=\"请选择所在地区\" clearable filterable>\n          <el-option\n            v-for=\"province in provinceOptions\"\n            :key=\"province.value\"\n            :label=\"province.label\"\n            :value=\"province.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['miniapp:investment:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['miniapp:investment:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['miniapp:investment:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['miniapp:investment:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"investmentList\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"项目ID\" align=\"center\" prop=\"investmentId\" width=\"80\" />\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" min-width=\"180\" show-overflow-tooltip />\n      <el-table-column label=\"封面图片\" align=\"center\" prop=\"coverImageUrl\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <image-preview :src=\"scope.row.coverImageUrl\" :width=\"80\" :height=\"50\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"融资轮次\" align=\"center\" prop=\"financingRound\" width=\"110\">\n        <template slot-scope=\"scope\">\n          <el-tag type=\"success\">{{ scope.row.financingRound }}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所属行业\" align=\"center\" prop=\"industryName\" min-width=\"120\" show-overflow-tooltip />\n      <el-table-column label=\"所在地区\" align=\"center\" prop=\"region\" width=\"100\" />\n      <el-table-column label=\"项目标签\" align=\"center\" prop=\"tags\" min-width=\"250\" show-overflow-tooltip>\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-for=\"(tag, index) in getTagArray(scope.row.tags)\"\n            :key=\"index\"\n            size=\"mini\"\n            style=\"margin-right: 5px; margin-bottom: 3px;\"\n            type=\"info\"\n          >\n            {{ tag }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"简介\" align=\"center\" prop=\"briefIntroduction\" min-width=\"200\" show-overflow-tooltip>\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.briefIntroduction }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"联系人\" align=\"center\" prop=\"contactPerson\" width=\"100\" />\n      <el-table-column label=\"浏览次数\" align=\"center\" prop=\"viewCount\" width=\"90\" />\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"70\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['miniapp:investment:query']\"\n          >查看</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['miniapp:investment:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['miniapp:investment:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改项目投资对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\n              <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"融资轮次\" prop=\"financingRound\">\n              <el-select v-model=\"form.financingRound\" placeholder=\"请选择融资轮次\">\n                <el-option label=\"种子轮\" value=\"种子轮\" />\n                <el-option label=\"天使轮\" value=\"天使轮\" />\n                <el-option label=\"Pre-A轮\" value=\"Pre-A轮\" />\n                <el-option label=\"A轮\" value=\"A轮\" />\n                <el-option label=\"A+轮\" value=\"A+轮\" />\n                <el-option label=\"B轮\" value=\"B轮\" />\n                <el-option label=\"B+轮\" value=\"B+轮\" />\n                <el-option label=\"C轮\" value=\"C轮\" />\n                <el-option label=\"C+轮\" value=\"C+轮\" />\n                <el-option label=\"D轮\" value=\"D轮\" />\n                <el-option label=\"E轮\" value=\"E轮\" />\n                <el-option label=\"F轮\" value=\"F轮\" />\n                <el-option label=\"IPO\" value=\"IPO\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所属行业\" prop=\"industryId\">\n              <el-select v-model=\"form.industryId\" placeholder=\"请选择所属行业\" filterable>\n                <el-option\n                  v-for=\"industry in industryOptions\"\n                  :key=\"industry.id\"\n                  :label=\"industry.nodeName\"\n                  :value=\"industry.id\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所在地区\" prop=\"region\">\n              <el-select v-model=\"form.region\" placeholder=\"请选择所在地区\" filterable>\n                <el-option\n                  v-for=\"province in provinceOptions\"\n                  :key=\"province.value\"\n                  :label=\"province.label\"\n                  :value=\"province.value\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系人\" prop=\"contactPerson\">\n              <el-input v-model=\"form.contactPerson\" placeholder=\"请输入联系人姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系方式\" prop=\"contactInfo\">\n              <el-input v-model=\"form.contactInfo\" placeholder=\"请输入联系方式\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\n              <el-input-number v-model=\"form.sortOrder\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"状态\" prop=\"status\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"项目标签\" prop=\"tags\">\n          <el-input v-model=\"form.tags\" placeholder=\"请输入项目标签，多个标签用逗号分隔\" />\n          <div style=\"margin-top: 5px; color: #909399; font-size: 12px;\">\n            示例：人工智能,大数据,云计算,物联网\n          </div>\n        </el-form-item>\n        <el-form-item label=\"封面图片\" prop=\"coverImageUrl\">\n          <image-upload v-model=\"form.coverImageUrl\"/>\n        </el-form-item>\n        <el-form-item label=\"详情顶图\" prop=\"topImageUrl\">\n          <image-upload v-model=\"form.topImageUrl\"/>\n        </el-form-item>\n        <el-form-item label=\"项目简介\" prop=\"briefIntroduction\">\n          <el-input v-model=\"form.briefIntroduction\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入项目简介\" />\n        </el-form-item>\n        <el-form-item label=\"项目详情\" prop=\"detailContent\">\n          <editor v-model=\"form.detailContent\" :min-height=\"192\"/>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listInvestment, getInvestment, delInvestment, addInvestment, updateInvestment } from \"@/api/miniapp/investment\";\nimport { listIndustryTree } from \"@/api/miniapp/investment\";\n\nexport default {\n  name: \"Investment\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 项目投资表格数据\n      investmentList: [],\n      // 行业树选项\n      industryOptions: [],\n      // 省份选项 - 直接使用简称\n      provinceOptions: [\n        { value: '北京', label: '北京' },\n        { value: '天津', label: '天津' },\n        { value: '河北', label: '河北' },\n        { value: '山西', label: '山西' },\n        { value: '内蒙古', label: '内蒙古' },\n        { value: '辽宁', label: '辽宁' },\n        { value: '吉林', label: '吉林' },\n        { value: '黑龙江', label: '黑龙江' },\n        { value: '上海', label: '上海' },\n        { value: '江苏', label: '江苏' },\n        { value: '浙江', label: '浙江' },\n        { value: '安徽', label: '安徽' },\n        { value: '福建', label: '福建' },\n        { value: '江西', label: '江西' },\n        { value: '山东', label: '山东' },\n        { value: '河南', label: '河南' },\n        { value: '湖北', label: '湖北' },\n        { value: '湖南', label: '湖南' },\n        { value: '广东', label: '广东' },\n        { value: '广西', label: '广西' },\n        { value: '海南', label: '海南' },\n        { value: '重庆', label: '重庆' },\n        { value: '四川', label: '四川' },\n        { value: '贵州', label: '贵州' },\n        { value: '云南', label: '云南' },\n        { value: '西藏', label: '西藏' },\n        { value: '陕西', label: '陕西' },\n        { value: '甘肃', label: '甘肃' },\n        { value: '青海', label: '青海' },\n        { value: '宁夏', label: '宁夏' },\n        { value: '新疆', label: '新疆' },\n        { value: '台湾', label: '台湾' },\n        { value: '香港', label: '香港' },\n        { value: '澳门', label: '澳门' }\n      ],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        projectName: null,\n        financingRound: null,\n        industryId: null,\n        region: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        projectName: [\n          { required: true, message: \"项目名称不能为空\", trigger: \"blur\" }\n        ],\n        financingRound: [\n          { required: true, message: \"融资轮次不能为空\", trigger: \"change\" }\n        ],\n        industryId: [\n          { required: true, message: \"所属行业不能为空\", trigger: \"change\" }\n        ],\n        region: [\n          { required: true, message: \"所在地区不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getIndustryTree();\n  },\n  methods: {\n    /** 查询项目投资列表 */\n    getList() {\n      this.loading = true;\n      listInvestment(this.queryParams).then(response => {\n        this.investmentList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 查询行业树 */\n    getIndustryTree() {\n      listIndustryTree().then(response => {\n        this.industryOptions = response.data;\n      });\n    },\n    /** 处理标签数组 */\n    getTagArray(tags) {\n      if (!tags) return [];\n      return tags.split(',').filter(tag => tag.trim() !== '');\n    },\n    /** 处理地区名称，去掉省市自治区等后缀 */\n    processRegionName(regionName) {\n      if (!regionName) return regionName;\n\n      // 定义需要去掉的后缀\n      const suffixes = ['省', '市', '自治区', '特别行政区', '壮族自治区', '回族自治区', '维吾尔自治区'];\n\n      let processedName = regionName;\n      for (const suffix of suffixes) {\n        if (processedName.endsWith(suffix)) {\n          processedName = processedName.substring(0, processedName.length - suffix.length);\n          break; // 只去掉第一个匹配的后缀\n        }\n      }\n\n      return processedName;\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        investmentId: null,\n        projectName: null,\n        coverImageUrl: null,\n        financingRound: null,\n        industryId: null,\n        region: null,\n        tags: null,\n        briefIntroduction: null,\n        detailContent: null,\n        topImageUrl: null,\n        contactPerson: null,\n        contactInfo: null,\n        viewCount: 0,\n        sortOrder: 0,\n        status: \"0\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.investmentId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加项目投资\";\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      this.reset();\n      const investmentId = row.investmentId || this.ids\n      getInvestment(investmentId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"查看项目投资详情\";\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const investmentId = row.investmentId || this.ids\n      getInvestment(investmentId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改项目投资\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.investmentId != null) {\n            updateInvestment(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addInvestment(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const investmentIds = row.investmentId || this.ids;\n      this.$modal.confirm('是否确认删除项目投资编号为\"' + investmentIds + '\"的数据项？').then(function() {\n        return delInvestment(investmentIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/investment/export', {\n        ...this.queryParams\n      }, `investment_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"]}]}