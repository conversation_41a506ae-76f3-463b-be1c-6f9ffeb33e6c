{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=template&id=82a94682&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1754104539620}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}