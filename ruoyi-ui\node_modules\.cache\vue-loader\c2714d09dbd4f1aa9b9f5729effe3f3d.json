{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\miniapp-test.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\miniapp-test.vue", "mtime": 1754103275226}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["miniapp-test.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "miniapp-test.vue", "sourceRoot": "src/views/miniapp/business/investment", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <h2>小程序端项目投资筛选测试</h2>\n    \n    <!-- 筛选条件 -->\n    <el-form :model=\"filterParams\" ref=\"filterForm\" size=\"small\" :inline=\"true\">\n      <el-form-item label=\"融资轮次\">\n        <el-select v-model=\"filterParams.financingRound\" placeholder=\"请选择融资轮次\" clearable>\n          <el-option label=\"种子轮\" value=\"种子轮\" />\n          <el-option label=\"天使轮\" value=\"天使轮\" />\n          <el-option label=\"A轮融资\" value=\"A轮融资\" />\n          <el-option label=\"B轮融资\" value=\"B轮融资\" />\n          <el-option label=\"C轮融资\" value=\"C轮融资\" />\n        </el-select>\n      </el-form-item>\n      \n      <el-form-item label=\"所在地区\">\n        <el-select v-model=\"filterParams.region\" placeholder=\"请选择地区\" clearable>\n          <el-option label=\"北京\" value=\"北京\" />\n          <el-option label=\"上海\" value=\"上海\" />\n          <el-option label=\"深圳\" value=\"深圳\" />\n          <el-option label=\"天津\" value=\"天津\" />\n          <el-option label=\"杭州\" value=\"杭州\" />\n          <el-option label=\"广州\" value=\"广州\" />\n          <el-option label=\"成都\" value=\"成都\" />\n          <el-option label=\"重庆\" value=\"重庆\" />\n        </el-select>\n      </el-form-item>\n      \n      <el-form-item label=\"行业\">\n        <el-select v-model=\"filterParams.industryId\" placeholder=\"请选择行业\" clearable>\n          <el-option label=\"航空航天\" value=\"30\" />\n          <el-option label=\"硬科技\" value=\"31\" />\n          <el-option label=\"人工智能\" value=\"32\" />\n        </el-select>\n      </el-form-item>\n      \n      <el-form-item label=\"关键字\">\n        <el-input\n          v-model=\"filterParams.keyword\"\n          placeholder=\"请输入项目名称或关键字\"\n          clearable\n          style=\"width: 200px;\"\n        />\n      </el-form-item>\n      \n      <el-form-item>\n        <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\n        <el-button @click=\"handleReset\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 结果展示 -->\n    <el-divider>搜索结果</el-divider>\n    \n    <el-table v-loading=\"loading\" :data=\"investmentList\" border>\n      <el-table-column label=\"项目名称\" prop=\"projectName\" width=\"150\" />\n      <el-table-column label=\"融资轮次\" prop=\"financingRound\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag type=\"success\">{{ scope.row.financingRound }}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所属行业\" prop=\"industryName\" width=\"120\" />\n      <el-table-column label=\"所在地区\" prop=\"region\" width=\"100\" />\n      <el-table-column label=\"项目标签\" prop=\"tags\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-for=\"(tag, index) in getTagArray(scope.row.tags)\"\n            :key=\"index\"\n            size=\"mini\"\n            style=\"margin-right: 5px;\"\n            type=\"info\"\n          >\n            {{ tag }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"项目简介\" prop=\"briefIntroduction\" show-overflow-tooltip />\n      <el-table-column label=\"联系人\" prop=\"contactPerson\" width=\"100\" />\n    </el-table>\n    \n    <div style=\"margin-top: 20px; text-align: center;\">\n      <span>共找到 {{ investmentList.length }} 个项目</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { listEnabledInvestment } from \"@/api/miniapp/investment\";\n\nexport default {\n  name: \"MiniappInvestmentTest\",\n  data() {\n    return {\n      loading: false,\n      investmentList: [],\n      filterParams: {\n        financingRound: '',\n        region: '',\n        industryId: '',\n        keyword: ''\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询项目投资列表 */\n    getList() {\n      this.loading = true;\n      \n      // 构建查询参数，过滤空值\n      const params = {};\n      if (this.filterParams.financingRound) {\n        params.financingRound = this.filterParams.financingRound;\n      }\n      if (this.filterParams.region) {\n        params.region = this.filterParams.region;\n      }\n      if (this.filterParams.industryId) {\n        params.industryId = this.filterParams.industryId;\n      }\n      if (this.filterParams.keyword) {\n        params.keyword = this.filterParams.keyword;\n      }\n      \n      listEnabledInvestment(params).then(response => {\n        this.investmentList = response.data;\n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n      });\n    },\n    \n    /** 处理标签数组 */\n    getTagArray(tags) {\n      if (!tags) return [];\n      return tags.split(',').filter(tag => tag.trim() !== '');\n    },\n    \n    /** 搜索按钮操作 */\n    handleSearch() {\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    handleReset() {\n      this.filterParams = {\n        financingRound: '',\n        region: '',\n        industryId: '',\n        keyword: ''\n      };\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"]}]}