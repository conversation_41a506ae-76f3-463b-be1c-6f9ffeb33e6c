{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\miniapp-test.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\miniapp-test.vue", "mtime": 1754103275226}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_investment", "require", "name", "data", "loading", "investmentList", "filterParams", "financingRound", "region", "industryId", "keyword", "created", "getList", "methods", "_this", "params", "listEnabledInvestment", "then", "response", "catch", "getTagArray", "tags", "split", "filter", "tag", "trim", "handleSearch", "handleReset"], "sources": ["src/views/miniapp/business/investment/miniapp-test.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <h2>小程序端项目投资筛选测试</h2>\n    \n    <!-- 筛选条件 -->\n    <el-form :model=\"filterParams\" ref=\"filterForm\" size=\"small\" :inline=\"true\">\n      <el-form-item label=\"融资轮次\">\n        <el-select v-model=\"filterParams.financingRound\" placeholder=\"请选择融资轮次\" clearable>\n          <el-option label=\"种子轮\" value=\"种子轮\" />\n          <el-option label=\"天使轮\" value=\"天使轮\" />\n          <el-option label=\"A轮融资\" value=\"A轮融资\" />\n          <el-option label=\"B轮融资\" value=\"B轮融资\" />\n          <el-option label=\"C轮融资\" value=\"C轮融资\" />\n        </el-select>\n      </el-form-item>\n      \n      <el-form-item label=\"所在地区\">\n        <el-select v-model=\"filterParams.region\" placeholder=\"请选择地区\" clearable>\n          <el-option label=\"北京\" value=\"北京\" />\n          <el-option label=\"上海\" value=\"上海\" />\n          <el-option label=\"深圳\" value=\"深圳\" />\n          <el-option label=\"天津\" value=\"天津\" />\n          <el-option label=\"杭州\" value=\"杭州\" />\n          <el-option label=\"广州\" value=\"广州\" />\n          <el-option label=\"成都\" value=\"成都\" />\n          <el-option label=\"重庆\" value=\"重庆\" />\n        </el-select>\n      </el-form-item>\n      \n      <el-form-item label=\"行业\">\n        <el-select v-model=\"filterParams.industryId\" placeholder=\"请选择行业\" clearable>\n          <el-option label=\"航空航天\" value=\"30\" />\n          <el-option label=\"硬科技\" value=\"31\" />\n          <el-option label=\"人工智能\" value=\"32\" />\n        </el-select>\n      </el-form-item>\n      \n      <el-form-item label=\"关键字\">\n        <el-input\n          v-model=\"filterParams.keyword\"\n          placeholder=\"请输入项目名称或关键字\"\n          clearable\n          style=\"width: 200px;\"\n        />\n      </el-form-item>\n      \n      <el-form-item>\n        <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\n        <el-button @click=\"handleReset\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <!-- 结果展示 -->\n    <el-divider>搜索结果</el-divider>\n    \n    <el-table v-loading=\"loading\" :data=\"investmentList\" border>\n      <el-table-column label=\"项目名称\" prop=\"projectName\" width=\"150\" />\n      <el-table-column label=\"融资轮次\" prop=\"financingRound\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <el-tag type=\"success\">{{ scope.row.financingRound }}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所属行业\" prop=\"industryName\" width=\"120\" />\n      <el-table-column label=\"所在地区\" prop=\"region\" width=\"100\" />\n      <el-table-column label=\"项目标签\" prop=\"tags\" width=\"200\">\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-for=\"(tag, index) in getTagArray(scope.row.tags)\"\n            :key=\"index\"\n            size=\"mini\"\n            style=\"margin-right: 5px;\"\n            type=\"info\"\n          >\n            {{ tag }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"项目简介\" prop=\"briefIntroduction\" show-overflow-tooltip />\n      <el-table-column label=\"联系人\" prop=\"contactPerson\" width=\"100\" />\n    </el-table>\n    \n    <div style=\"margin-top: 20px; text-align: center;\">\n      <span>共找到 {{ investmentList.length }} 个项目</span>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { listEnabledInvestment } from \"@/api/miniapp/investment\";\n\nexport default {\n  name: \"MiniappInvestmentTest\",\n  data() {\n    return {\n      loading: false,\n      investmentList: [],\n      filterParams: {\n        financingRound: '',\n        region: '',\n        industryId: '',\n        keyword: ''\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询项目投资列表 */\n    getList() {\n      this.loading = true;\n      \n      // 构建查询参数，过滤空值\n      const params = {};\n      if (this.filterParams.financingRound) {\n        params.financingRound = this.filterParams.financingRound;\n      }\n      if (this.filterParams.region) {\n        params.region = this.filterParams.region;\n      }\n      if (this.filterParams.industryId) {\n        params.industryId = this.filterParams.industryId;\n      }\n      if (this.filterParams.keyword) {\n        params.keyword = this.filterParams.keyword;\n      }\n      \n      listEnabledInvestment(params).then(response => {\n        this.investmentList = response.data;\n        this.loading = false;\n      }).catch(() => {\n        this.loading = false;\n      });\n    },\n    \n    /** 处理标签数组 */\n    getTagArray(tags) {\n      if (!tags) return [];\n      return tags.split(',').filter(tag => tag.trim() !== '');\n    },\n    \n    /** 搜索按钮操作 */\n    handleSearch() {\n      this.getList();\n    },\n    \n    /** 重置按钮操作 */\n    handleReset() {\n      this.filterParams = {\n        financingRound: '',\n        region: '',\n        industryId: '',\n        keyword: ''\n      };\n      this.getList();\n    }\n  }\n};\n</script>\n\n<style scoped>\n.app-container {\n  padding: 20px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;AAwFA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,cAAA;MACAC,YAAA;QACAC,cAAA;QACAC,MAAA;QACAC,UAAA;QACAC,OAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAV,OAAA;;MAEA;MACA,IAAAW,MAAA;MACA,SAAAT,YAAA,CAAAC,cAAA;QACAQ,MAAA,CAAAR,cAAA,QAAAD,YAAA,CAAAC,cAAA;MACA;MACA,SAAAD,YAAA,CAAAE,MAAA;QACAO,MAAA,CAAAP,MAAA,QAAAF,YAAA,CAAAE,MAAA;MACA;MACA,SAAAF,YAAA,CAAAG,UAAA;QACAM,MAAA,CAAAN,UAAA,QAAAH,YAAA,CAAAG,UAAA;MACA;MACA,SAAAH,YAAA,CAAAI,OAAA;QACAK,MAAA,CAAAL,OAAA,QAAAJ,YAAA,CAAAI,OAAA;MACA;MAEA,IAAAM,iCAAA,EAAAD,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAT,cAAA,GAAAa,QAAA,CAAAf,IAAA;QACAW,KAAA,CAAAV,OAAA;MACA,GAAAe,KAAA;QACAL,KAAA,CAAAV,OAAA;MACA;IACA;IAEA,aACAgB,WAAA,WAAAA,YAAAC,IAAA;MACA,KAAAA,IAAA;MACA,OAAAA,IAAA,CAAAC,KAAA,MAAAC,MAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,CAAAC,IAAA;MAAA;IACA;IAEA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAd,OAAA;IACA;IAEA,aACAe,WAAA,WAAAA,YAAA;MACA,KAAArB,YAAA;QACAC,cAAA;QACAC,MAAA;QACAC,UAAA;QACAC,OAAA;MACA;MACA,KAAAE,OAAA;IACA;EACA;AACA", "ignoreList": []}]}