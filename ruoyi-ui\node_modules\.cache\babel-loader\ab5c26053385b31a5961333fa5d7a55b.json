{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\index.vue", "mtime": 1754103601119}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_investment", "require", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "investmentList", "industryOptions", "provinceOptions", "value", "label", "title", "open", "queryParams", "pageNum", "pageSize", "projectName", "financingRound", "industryId", "region", "status", "form", "rules", "required", "message", "trigger", "created", "getList", "getIndustryTree", "methods", "_this", "listInvestment", "then", "response", "rows", "_this2", "listIndustryTree", "getTagArray", "tags", "split", "filter", "tag", "trim", "processRegionName", "regionName", "suffixes", "processedName", "_i", "_suffixes", "length", "suffix", "endsWith", "substring", "cancel", "reset", "investmentId", "coverImageUrl", "briefIntroduction", "detailContent", "topImageUrl", "<PERSON><PERSON><PERSON>", "contactInfo", "viewCount", "sortOrder", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "handleView", "row", "_this3", "getInvestment", "handleUpdate", "_this4", "submitForm", "_this5", "$refs", "validate", "valid", "updateInvestment", "$modal", "msgSuccess", "addInvestment", "handleDelete", "_this6", "investmentIds", "confirm", "delInvestment", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/miniapp/business/investment/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\n        <el-input\n          v-model=\"queryParams.projectName\"\n          placeholder=\"请输入项目名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"融资轮次\" prop=\"financingRound\">\n        <el-select v-model=\"queryParams.financingRound\" placeholder=\"请选择融资轮次\" clearable>\n          <el-option label=\"种子轮\" value=\"种子轮\" />\n          <el-option label=\"天使轮\" value=\"天使轮\" />\n          <el-option label=\"Pre-A轮\" value=\"Pre-A轮\" />\n          <el-option label=\"A轮\" value=\"A轮\" />\n          <el-option label=\"A+轮\" value=\"A+轮\" />\n          <el-option label=\"B轮\" value=\"B轮\" />\n          <el-option label=\"B+轮\" value=\"B+轮\" />\n          <el-option label=\"C轮\" value=\"C轮\" />\n          <el-option label=\"C+轮\" value=\"C+轮\" />\n          <el-option label=\"D轮\" value=\"D轮\" />\n          <el-option label=\"E轮\" value=\"E轮\" />\n          <el-option label=\"F轮\" value=\"F轮\" />\n          <el-option label=\"IPO\" value=\"IPO\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"所在地区\" prop=\"region\">\n        <el-select v-model=\"queryParams.region\" placeholder=\"请选择所在地区\" clearable filterable>\n          <el-option\n            v-for=\"province in provinceOptions\"\n            :key=\"province.value\"\n            :label=\"province.label\"\n            :value=\"province.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\n          <el-option\n            v-for=\"dict in dict.type.sys_normal_disable\"\n            :key=\"dict.value\"\n            :label=\"dict.label\"\n            :value=\"dict.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['miniapp:investment:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['miniapp:investment:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['miniapp:investment:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['miniapp:investment:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"investmentList\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"项目ID\" align=\"center\" prop=\"investmentId\" width=\"80\" />\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" min-width=\"180\" show-overflow-tooltip />\n      <el-table-column label=\"封面图片\" align=\"center\" prop=\"coverImageUrl\" width=\"120\">\n        <template slot-scope=\"scope\">\n          <image-preview :src=\"scope.row.coverImageUrl\" :width=\"80\" :height=\"50\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"融资轮次\" align=\"center\" prop=\"financingRound\" width=\"110\">\n        <template slot-scope=\"scope\">\n          <el-tag type=\"success\">{{ scope.row.financingRound }}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"所属行业\" align=\"center\" prop=\"industryName\" min-width=\"120\" show-overflow-tooltip />\n      <el-table-column label=\"所在地区\" align=\"center\" prop=\"region\" width=\"100\" />\n      <el-table-column label=\"项目标签\" align=\"center\" prop=\"tags\" min-width=\"250\" show-overflow-tooltip>\n        <template slot-scope=\"scope\">\n          <el-tag\n            v-for=\"(tag, index) in getTagArray(scope.row.tags)\"\n            :key=\"index\"\n            size=\"mini\"\n            style=\"margin-right: 5px; margin-bottom: 3px;\"\n            type=\"info\"\n          >\n            {{ tag }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"简介\" align=\"center\" prop=\"briefIntroduction\" min-width=\"200\" show-overflow-tooltip>\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.briefIntroduction }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"联系人\" align=\"center\" prop=\"contactPerson\" width=\"100\" />\n      <el-table-column label=\"浏览次数\" align=\"center\" prop=\"viewCount\" width=\"90\" />\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"70\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleView(scope.row)\"\n            v-hasPermi=\"['miniapp:investment:query']\"\n          >查看</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['miniapp:investment:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['miniapp:investment:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改项目投资对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\n              <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"融资轮次\" prop=\"financingRound\">\n              <el-select v-model=\"form.financingRound\" placeholder=\"请选择融资轮次\">\n                <el-option label=\"种子轮\" value=\"种子轮\" />\n                <el-option label=\"天使轮\" value=\"天使轮\" />\n                <el-option label=\"Pre-A轮\" value=\"Pre-A轮\" />\n                <el-option label=\"A轮\" value=\"A轮\" />\n                <el-option label=\"A+轮\" value=\"A+轮\" />\n                <el-option label=\"B轮\" value=\"B轮\" />\n                <el-option label=\"B+轮\" value=\"B+轮\" />\n                <el-option label=\"C轮\" value=\"C轮\" />\n                <el-option label=\"C+轮\" value=\"C+轮\" />\n                <el-option label=\"D轮\" value=\"D轮\" />\n                <el-option label=\"E轮\" value=\"E轮\" />\n                <el-option label=\"F轮\" value=\"F轮\" />\n                <el-option label=\"IPO\" value=\"IPO\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所属行业\" prop=\"industryId\">\n              <el-select v-model=\"form.industryId\" placeholder=\"请选择所属行业\" filterable>\n                <el-option\n                  v-for=\"industry in industryOptions\"\n                  :key=\"industry.id\"\n                  :label=\"industry.nodeName\"\n                  :value=\"industry.id\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所在地区\" prop=\"region\">\n              <el-select v-model=\"form.region\" placeholder=\"请选择所在地区\" filterable>\n                <el-option\n                  v-for=\"province in provinceOptions\"\n                  :key=\"province.value\"\n                  :label=\"province.label\"\n                  :value=\"province.value\"\n                />\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系人\" prop=\"contactPerson\">\n              <el-input v-model=\"form.contactPerson\" placeholder=\"请输入联系人姓名\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"联系方式\" prop=\"contactInfo\">\n              <el-input v-model=\"form.contactInfo\" placeholder=\"请输入联系方式\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\n              <el-input-number v-model=\"form.sortOrder\" controls-position=\"right\" :min=\"0\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"状态\" prop=\"status\">\n              <el-radio-group v-model=\"form.status\">\n                <el-radio\n                  v-for=\"dict in dict.type.sys_normal_disable\"\n                  :key=\"dict.value\"\n                  :label=\"dict.value\"\n                >{{dict.label}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"项目标签\" prop=\"tags\">\n          <el-input v-model=\"form.tags\" placeholder=\"请输入项目标签，多个标签用逗号分隔\" />\n          <div style=\"margin-top: 5px; color: #909399; font-size: 12px;\">\n            示例：人工智能,大数据,云计算,物联网\n          </div>\n        </el-form-item>\n        <el-form-item label=\"封面图片\" prop=\"coverImageUrl\">\n          <image-upload v-model=\"form.coverImageUrl\"/>\n        </el-form-item>\n        <el-form-item label=\"详情顶图\" prop=\"topImageUrl\">\n          <image-upload v-model=\"form.topImageUrl\"/>\n        </el-form-item>\n        <el-form-item label=\"项目简介\" prop=\"briefIntroduction\">\n          <el-input v-model=\"form.briefIntroduction\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入项目简介\" />\n        </el-form-item>\n        <el-form-item label=\"项目详情\" prop=\"detailContent\">\n          <editor v-model=\"form.detailContent\" :min-height=\"192\"/>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listInvestment, getInvestment, delInvestment, addInvestment, updateInvestment } from \"@/api/miniapp/investment\";\nimport { listIndustryTree } from \"@/api/miniapp/investment\";\n\nexport default {\n  name: \"Investment\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 项目投资表格数据\n      investmentList: [],\n      // 行业树选项\n      industryOptions: [],\n      // 省份选项 - 直接使用简称\n      provinceOptions: [\n        { value: '北京', label: '北京' },\n        { value: '天津', label: '天津' },\n        { value: '河北', label: '河北' },\n        { value: '山西', label: '山西' },\n        { value: '内蒙古', label: '内蒙古' },\n        { value: '辽宁', label: '辽宁' },\n        { value: '吉林', label: '吉林' },\n        { value: '黑龙江', label: '黑龙江' },\n        { value: '上海', label: '上海' },\n        { value: '江苏', label: '江苏' },\n        { value: '浙江', label: '浙江' },\n        { value: '安徽', label: '安徽' },\n        { value: '福建', label: '福建' },\n        { value: '江西', label: '江西' },\n        { value: '山东', label: '山东' },\n        { value: '河南', label: '河南' },\n        { value: '湖北', label: '湖北' },\n        { value: '湖南', label: '湖南' },\n        { value: '广东', label: '广东' },\n        { value: '广西', label: '广西' },\n        { value: '海南', label: '海南' },\n        { value: '重庆', label: '重庆' },\n        { value: '四川', label: '四川' },\n        { value: '贵州', label: '贵州' },\n        { value: '云南', label: '云南' },\n        { value: '西藏', label: '西藏' },\n        { value: '陕西', label: '陕西' },\n        { value: '甘肃', label: '甘肃' },\n        { value: '青海', label: '青海' },\n        { value: '宁夏', label: '宁夏' },\n        { value: '新疆', label: '新疆' },\n        { value: '台湾', label: '台湾' },\n        { value: '香港', label: '香港' },\n        { value: '澳门', label: '澳门' }\n      ],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        projectName: null,\n        financingRound: null,\n        industryId: null,\n        region: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        projectName: [\n          { required: true, message: \"项目名称不能为空\", trigger: \"blur\" }\n        ],\n        financingRound: [\n          { required: true, message: \"融资轮次不能为空\", trigger: \"change\" }\n        ],\n        industryId: [\n          { required: true, message: \"所属行业不能为空\", trigger: \"change\" }\n        ],\n        region: [\n          { required: true, message: \"所在地区不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getIndustryTree();\n  },\n  methods: {\n    /** 查询项目投资列表 */\n    getList() {\n      this.loading = true;\n      listInvestment(this.queryParams).then(response => {\n        this.investmentList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 查询行业树 */\n    getIndustryTree() {\n      listIndustryTree().then(response => {\n        this.industryOptions = response.data;\n      });\n    },\n    /** 处理标签数组 */\n    getTagArray(tags) {\n      if (!tags) return [];\n      return tags.split(',').filter(tag => tag.trim() !== '');\n    },\n    /** 处理地区名称，去掉省市自治区等后缀 */\n    processRegionName(regionName) {\n      if (!regionName) return regionName;\n\n      // 定义需要去掉的后缀\n      const suffixes = ['省', '市', '自治区', '特别行政区', '壮族自治区', '回族自治区', '维吾尔自治区'];\n\n      let processedName = regionName;\n      for (const suffix of suffixes) {\n        if (processedName.endsWith(suffix)) {\n          processedName = processedName.substring(0, processedName.length - suffix.length);\n          break; // 只去掉第一个匹配的后缀\n        }\n      }\n\n      return processedName;\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        investmentId: null,\n        projectName: null,\n        coverImageUrl: null,\n        financingRound: null,\n        industryId: null,\n        region: null,\n        tags: null,\n        briefIntroduction: null,\n        detailContent: null,\n        topImageUrl: null,\n        contactPerson: null,\n        contactInfo: null,\n        viewCount: 0,\n        sortOrder: 0,\n        status: \"0\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.investmentId)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加项目投资\";\n    },\n    /** 查看按钮操作 */\n    handleView(row) {\n      this.reset();\n      const investmentId = row.investmentId || this.ids\n      getInvestment(investmentId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"查看项目投资详情\";\n      });\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const investmentId = row.investmentId || this.ids\n      getInvestment(investmentId).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改项目投资\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.investmentId != null) {\n            updateInvestment(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addInvestment(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const investmentIds = row.investmentId || this.ids;\n      this.$modal.confirm('是否确认删除项目投资编号为\"' + investmentIds + '\"的数据项？').then(function() {\n        return delInvestment(investmentIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/investment/export', {\n        ...this.queryParams\n      }, `investment_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;AA8SA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,eAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAN,WAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,cAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,UAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,MAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACA,eACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAA9B,OAAA;MACA,IAAA+B,0BAAA,OAAAlB,WAAA,EAAAmB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,cAAA,GAAA2B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAzB,KAAA,GAAA4B,QAAA,CAAA5B,KAAA;QACAyB,KAAA,CAAA9B,OAAA;MACA;IACA;IACA,YACA4B,eAAA,WAAAA,gBAAA;MAAA,IAAAO,MAAA;MACA,IAAAC,4BAAA,IAAAJ,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAA5B,eAAA,GAAA0B,QAAA,CAAAlC,IAAA;MACA;IACA;IACA,aACAsC,WAAA,WAAAA,YAAAC,IAAA;MACA,KAAAA,IAAA;MACA,OAAAA,IAAA,CAAAC,KAAA,MAAAC,MAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,CAAAC,IAAA;MAAA;IACA;IACA,wBACAC,iBAAA,WAAAA,kBAAAC,UAAA;MACA,KAAAA,UAAA,SAAAA,UAAA;;MAEA;MACA,IAAAC,QAAA;MAEA,IAAAC,aAAA,GAAAF,UAAA;MACA,SAAAG,EAAA,MAAAC,SAAA,GAAAH,QAAA,EAAAE,EAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,EAAA;QAAA,IAAAG,MAAA,GAAAF,SAAA,CAAAD,EAAA;QACA,IAAAD,aAAA,CAAAK,QAAA,CAAAD,MAAA;UACAJ,aAAA,GAAAA,aAAA,CAAAM,SAAA,IAAAN,aAAA,CAAAG,MAAA,GAAAC,MAAA,CAAAD,MAAA;UACA;QACA;MACA;MAEA,OAAAH,aAAA;IACA;IACA;IACAO,MAAA,WAAAA,OAAA;MACA,KAAAzC,IAAA;MACA,KAAA0C,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAjC,IAAA;QACAkC,YAAA;QACAvC,WAAA;QACAwC,aAAA;QACAvC,cAAA;QACAC,UAAA;QACAC,MAAA;QACAmB,IAAA;QACAmB,iBAAA;QACAC,aAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,SAAA;QACAC,SAAA;QACA3C,MAAA;MACA;MACA,KAAA4C,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAApD,WAAA,CAAAC,OAAA;MACA,KAAAa,OAAA;IACA;IACA,aACAuC,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnE,GAAA,GAAAmE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAf,YAAA;MAAA;MACA,KAAArD,MAAA,GAAAkE,SAAA,CAAAnB,MAAA;MACA,KAAA9C,QAAA,IAAAiE,SAAA,CAAAnB,MAAA;IACA;IACA,aACAsB,SAAA,WAAAA,UAAA;MACA,KAAAjB,KAAA;MACA,KAAA1C,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA6D,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAApB,KAAA;MACA,IAAAC,YAAA,GAAAkB,GAAA,CAAAlB,YAAA,SAAAtD,GAAA;MACA,IAAA0E,yBAAA,EAAApB,YAAA,EAAAvB,IAAA,WAAAC,QAAA;QACAyC,MAAA,CAAArD,IAAA,GAAAY,QAAA,CAAAlC,IAAA;QACA2E,MAAA,CAAA9D,IAAA;QACA8D,MAAA,CAAA/D,KAAA;MACA;IACA;IACA,aACAiE,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAAvB,KAAA;MACA,IAAAC,YAAA,GAAAkB,GAAA,CAAAlB,YAAA,SAAAtD,GAAA;MACA,IAAA0E,yBAAA,EAAApB,YAAA,EAAAvB,IAAA,WAAAC,QAAA;QACA4C,MAAA,CAAAxD,IAAA,GAAAY,QAAA,CAAAlC,IAAA;QACA8E,MAAA,CAAAjE,IAAA;QACAiE,MAAA,CAAAlE,KAAA;MACA;IACA;IACA,WACAmE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA1D,IAAA,CAAAkC,YAAA;YACA,IAAA4B,4BAAA,EAAAJ,MAAA,CAAA1D,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACA8C,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAnE,IAAA;cACAmE,MAAA,CAAApD,OAAA;YACA;UACA;YACA,IAAA2D,yBAAA,EAAAP,MAAA,CAAA1D,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACA8C,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAnE,IAAA;cACAmE,MAAA,CAAApD,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA4D,YAAA,WAAAA,aAAAd,GAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,aAAA,GAAAhB,GAAA,CAAAlB,YAAA,SAAAtD,GAAA;MACA,KAAAmF,MAAA,CAAAM,OAAA,oBAAAD,aAAA,aAAAzD,IAAA;QACA,WAAA2D,yBAAA,EAAAF,aAAA;MACA,GAAAzD,IAAA;QACAwD,MAAA,CAAA7D,OAAA;QACA6D,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,kCAAAC,cAAA,CAAAC,OAAA,MACA,KAAAnF,WAAA,iBAAAoF,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}