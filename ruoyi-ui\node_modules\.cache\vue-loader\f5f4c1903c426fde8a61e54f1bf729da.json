{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1754104539620}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}